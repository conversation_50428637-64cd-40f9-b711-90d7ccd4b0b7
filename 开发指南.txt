===============================================================================
                            Miko邮箱系统开发指南
===============================================================================

版本: v1.0
更新时间: 2025-01-24
作者: Miko Team (QQ: 2014131458)

===============================================================================
目录
===============================================================================

1. 开发环境搭建
2. 项目结构说明
3. 开发规范
4. 代码风格
5. 数据库设计
6. API开发指南
7. 前端开发指南
8. 测试指南
9. 调试技巧
10. 贡献指南

===============================================================================
1. 开发环境搭建
===============================================================================

1.1 必需软件
- Go 1.21+
- Git
- VS Code 或 GoLand
- SQLite Browser (可选)
- Postman (API测试)

1.2 Go开发环境配置
```bash
# 设置Go模块代理
go env -w GOPROXY=https://goproxy.cn,direct
go env -w GOSUMDB=sum.golang.google.cn

# 设置Go工作目录
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin
```

1.3 IDE配置

VS Code推荐插件:
- Go (官方Go插件)
- GitLens
- REST Client
- SQLite Viewer
- YAML

GoLand配置:
- 启用Go Modules
- 配置代码格式化
- 设置Live Templates

1.4 克隆项目
```bash
git clone <repository-url>
cd miko邮箱
go mod tidy
```

===============================================================================
2. 项目结构说明
===============================================================================

2.1 目录结构
后面完善