<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .admin-container {
            position: relative;
            z-index: 1;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #ffc107;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }

        .input-group-text {
            border: 2px solid #e9ecef;
            border-radius: 12px 0 0 12px;
            background: #f8f9fa;
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            color: #000;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.4);
            color: #000;
        }

        .floating-icons {
            position: absolute;
            animation: float 6s ease-in-out infinite;
            opacity: 0.1;
            color: white;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(10deg);
            }
        }

        .alert-info {
            border-radius: 12px;
            border: 2px solid rgba(13, 202, 240, 0.2);
        }
    </style>
</head>

<body>
    <!-- 浮动图标 -->
    <div class="floating-icons" style="top: 10%; left: 10%; font-size: 3rem; animation-delay: 0s;">
        <i class="bi bi-gear-wide-connected"></i>
    </div>
    <div class="floating-icons" style="top: 20%; right: 15%; font-size: 2.5rem; animation-delay: -2s;">
        <i class="bi bi-shield-lock"></i>
    </div>
    <div class="floating-icons" style="bottom: 30%; left: 20%; font-size: 2rem; animation-delay: -4s;">
        <i class="bi bi-database"></i>
    </div>
    <div class="floating-icons" style="bottom: 20%; right: 10%; font-size: 2.8rem; animation-delay: -1s;">
        <i class="bi bi-graph-up"></i>
    </div>

    <div class="container-fluid admin-container min-vh-100 d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card shadow-lg">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                {{if .site_logo}}
                                <img src="{{.site_logo}}" alt="{{.site_name}}"
                                    style="max-width: 120px; max-height: 60px; margin-bottom: 16px;">
                                {{else}}
                                <i class="bi bi-shield-lock-fill text-warning" style="font-size: 3rem;"></i>
                                {{end}}
                                <h3 class="mt-3">管理员登录</h3>
                                <p class="text-muted">登录到{{.site_name}}管理员控制面板</p>
                            </div>

                            <form id="adminLoginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">管理员用户名</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person-badge"></i>
                                        </span>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">管理员密码</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-key"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password"
                                            required>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="bi bi-shield-check me-2"></i>
                                        管理员登录
                                    </button>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <small>默认管理员账户: admin / 123456</small>
                                </div>
                            </div>

                            <div class="text-center mt-3">
                                <a href="/login" class="text-muted small">
                                    <i class="bi bi-person me-1"></i>
                                    普通用户登录
                                </a>
                            </div>

                            <div class="text-center mt-2">
                                <a href="/" class="text-muted small">
                                    <i class="bi bi-house me-1"></i>
                                    返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 mb-0">正在验证管理员身份...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        document.getElementById('adminLoginForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('请填写管理员用户名和密码', 'warning');
                return;
            }

            // 显示加载提示
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();

            try {
                const response = await axios.post('/api/admin/login', {
                    username: username,
                    password: password
                });

                if (response.data.success) {
                    showAlert('管理员登录成功！正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = '/admin/dashboard';
                    }, 1000);
                } else {
                    showAlert(response.data.message || '管理员登录失败', 'danger');
                }
            } catch (error) {
                console.error('Admin login error:', error);
                if (error.response && error.response.data) {
                    showAlert(error.response.data.message || '管理员登录失败', 'danger');
                } else {
                    showAlert('网络错误，请稍后重试', 'danger');
                }
            } finally {
                loadingModal.hide();
            }
        });

        function showAlert(message, type) {
            document.getElementById('alertMessage').textContent = message;
            const toast = new bootstrap.Toast(document.getElementById('alertToast'));
            toast.show();
        }
    </script>

    <!-- 全局提示框 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="alertToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle me-2"></i>
                <strong class="me-auto">系统提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="alertMessage">
            </div>
        </div>
    </div>
</body>

</html>""