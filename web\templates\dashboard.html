<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-radius: 0 20px 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .sidebar .nav-link {
            color: #667eea !important;
            border-radius: 12px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            color: white !important;
            transform: translateX(5px);
        }
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px 0 0 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .border-left-primary {
            border-left: 4px solid #667eea !important;
        }
        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }
        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }
        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }
        .btn {
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .quick-action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            margin: 0.5rem;
        }
        .quick-action-btn:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .stats-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-left: 4px solid;
        }
        .table {
            border-radius: 12px;
            overflow: hidden;
        }
        .badge {
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- 侧边栏 -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column h-100 p-3 text-dark">
                <a href="/dashboard" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-decoration-none">
                    {{if .site_logo}}
                        <img src="{{.site_logo}}" alt="{{.site_name}}" style="height: 32px; margin-right: 8px;">
                    {{else}}
                        <i class="bi bi-envelope-heart me-2 text-primary"></i>
                    {{end}}
                    <span class="fs-4 fw-bold text-primary">{{.site_name}}</span>
                </a>
                <hr>
                <ul class="nav nav-pills flex-column mb-auto">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link active">
                            <i class="bi bi-speedometer2 me-2"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/inbox" class="nav-link">
                            <i class="bi bi-inbox me-2"></i>
                            收件箱
                        </a>
                    </li>
                    <li>
                        <a href="/sent" class="nav-link">
                            <i class="bi bi-send me-2"></i>
                            已发送
                        </a>
                    </li>
                    <li>
                        <a href="/compose" class="nav-link">
                            <i class="bi bi-pencil-square me-2"></i>
                            写邮件
                        </a>
                    </li>
                    <li>
                        <a href="/forward" class="nav-link">
                            <i class="bi bi-trash me-2"></i>
                            转邮件
                        </a>
                    </li>
                    <li>
                        <a href="/mailboxes" class="nav-link">
                            <i class="bi bi-collection me-2"></i>
                            邮箱管理
                        </a>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link">
                            <i class="bi bi-gear me-2"></i>
                            设置
                        </a>
                    </li>
                </ul>
                <hr>
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle text-primary" id="dropdownUser1" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <strong>{{.username}}</strong>
                    </a>
                    <ul class="dropdown-menu shadow">
                        <li><a class="dropdown-item" href="/settings">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="container-fluid p-4">
                <!-- 欢迎标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 text-primary fw-bold">欢迎回来，{{.username}}！</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" onclick="location.href='/compose'">
                            <i class="bi bi-plus-circle me-2"></i>
                            写邮件
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">我的邮箱</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalMailboxes">-</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-collection display-4 text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">未读邮件</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="unreadEmails">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-envelope display-4 text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">已发送</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="sentEmails">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-send display-4 text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">贡献度</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="contribution">0</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-star display-4 text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                            </div>
                            <div class="card-body text-center">
                                <button class="btn quick-action-btn" onclick="location.href='/compose'">
                                    <i class="bi bi-pencil-square me-2"></i>
                                    写邮件
                                </button>
                                <button class="btn quick-action-btn" onclick="location.href='/mailboxes'">
                                    <i class="bi bi-collection me-2"></i>
                                    管理邮箱
                                </button>
                                <button class="btn quick-action-btn" onclick="location.href='/inbox'">
                                    <i class="bi bi-inbox me-2"></i>
                                    查看收件箱
                                </button>
                                <button class="btn quick-action-btn" onclick="refreshData()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 我的邮箱列表 -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">我的邮箱列表</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>邮箱地址</th>
                                        <th>创建时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="mailboxesTableBody">
                                    <tr>
                                        <td colspan="4" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="mt-2">正在加载邮箱列表...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="/static/js/common.js"></script>
<script>
// 刷新数据
async function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>刷新中...';
    refreshBtn.disabled = true;
    
    try {
        await loadStats();
        await loadMailboxes();
    } catch (error) {
        console.error('刷新数据失败:', error);
    } finally {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    }
}

// 退出登录
async function logout() {
    try {
        await axios.post('/api/logout');
        window.location.href = '/login';
    } catch (error) {
        console.error('Logout failed:', error);
        window.location.href = '/login';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    loadMailboxes();
});

async function loadStats() {
    try {
        // 获取用户资料
        const profileResponse = await axios.get('/api/profile');
        if (profileResponse.data.success) {
            const user = profileResponse.data.data;
            document.getElementById('contribution').textContent = user.contribution || 0;
        }

        // 获取用户统计信息
        const statsResponse = await axios.get('/api/mailboxes/stats');
        if (statsResponse.data.success) {
            const stats = statsResponse.data.data;
            document.getElementById('totalMailboxes').textContent = stats.total_mailboxes || 0;
            document.getElementById('unreadEmails').textContent = stats.unread_emails || 0;
            document.getElementById('sentEmails').textContent = stats.sent_emails || 0;
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

async function loadMailboxes() {
    try {
        const response = await axios.get('/api/mailboxes');
        if (response.data.success) {
            const mailboxes = response.data.data;
            displayMailboxes(mailboxes);
        }
    } catch (error) {
        console.error('Failed to load mailboxes:', error);
        document.getElementById('mailboxesTableBody').innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        加载邮箱列表失败，请稍后重试
                    </div>
                </td>
            </tr>
        `;
    }
}

function displayMailboxes(mailboxes) {
    const tbody = document.getElementById('mailboxesTableBody');
    
    if (mailboxes.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h5 class="mt-3">还没有邮箱</h5>
                    <p class="text-muted">点击下方按钮创建您的第一个邮箱</p>
                    <a href="/mailboxes" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        创建邮箱
                    </a>
                </td>
            </tr>
        `;
        return;
    }

    let html = '';
    mailboxes.forEach(mailbox => {
        const createdAt = new Date(mailbox.created_at).toLocaleString('zh-CN');
        const status = mailbox.status === 'active' ? '正常' : '禁用';
        const statusClass = mailbox.status === 'active' ? 'bg-success' : 'bg-secondary';
        html += `
            <tr>
                <td>
                    <i class="bi bi-envelope me-2 text-primary"></i>
                    <strong>${mailbox.email}</strong>
                </td>
                <td>${createdAt}</td>
                <td>
                    <span class="badge ${statusClass}">${status}</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="location.href='/inbox?mailbox=${encodeURIComponent(mailbox.email)}'">
                            <i class="bi bi-inbox"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="location.href='/compose?from=${encodeURIComponent(mailbox.email)}'">
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}
</script>
</body>
</html>