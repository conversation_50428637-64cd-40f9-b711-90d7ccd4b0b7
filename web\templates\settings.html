<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人设置 - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-radius: 0 20px 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .sidebar .nav-link {
            color: #667eea !important;
            border-radius: 12px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            color: white !important;
            transform: translateX(5px);
        }
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px 0 0 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn {
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .stats-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .stats-card i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stats-card h5 {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        .stats-card .text-primary i { color: #667eea !important; }
        .stats-card .text-success i { color: #28a745 !important; }
        .stats-card .text-info i { color: #17a2b8 !important; }
        .stats-card .text-warning i { color: #ffc107 !important; }
        .btn-change-password {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .btn-change-password:hover {
            background: linear-gradient(45deg, #fd7e14, #ffc107);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .invite-code-container {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 1rem;
            border: 2px dashed #667eea;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- 侧边栏 -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column h-100 p-3 text-dark">
                <a href="/dashboard" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-decoration-none">
                    {{if .site_logo}}
                        <img src="{{.site_logo}}" alt="{{.site_name}}" style="height: 32px; margin-right: 8px;">
                    {{else}}
                        <i class="bi bi-envelope-heart me-2 text-primary"></i>
                    {{end}}
                    <span class="fs-4 fw-bold text-primary">{{.site_name}}</span>
                </a>
                <hr>
                <ul class="nav nav-pills flex-column mb-auto">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link">
                            <i class="bi bi-speedometer2 me-2"></i>
                            仪表板
                        </a>
                    </li>
                    <li>
                        <a href="/inbox" class="nav-link">
                            <i class="bi bi-inbox me-2"></i>
                            收件箱
                        </a>
                    </li>
                    <li>
                        <a href="/sent" class="nav-link">
                            <i class="bi bi-send me-2"></i>
                            已发送
                        </a>
                    </li>
                    <li>
                        <a href="/compose" class="nav-link">
                            <i class="bi bi-pencil-square me-2"></i>
                            写邮件
                        </a>
                    </li>
                    <li>
                        <a href="/forward" class="nav-link">
                            <i class="bi bi-arrow-right-circle me-2"></i>
                            转邮件
                        </a>
                    </li>
                    <li>
                        <a href="/mailboxes" class="nav-link">
                            <i class="bi bi-collection me-2"></i>
                            邮箱管理
                        </a>
                    </li>
                    <li>
                        <a href="/settings" class="nav-link active">
                            <i class="bi bi-gear me-2"></i>
                            设置
                        </a>
                    </li>
                </ul>
                <hr>
                <div class="dropdown">
                    <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle text-primary" id="dropdownUser1" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-2"></i>
                        <strong id="navUsername">{{.username}}</strong>
                    </a>
                    <ul class="dropdown-menu shadow">
                        <li><a class="dropdown-item" href="/settings">个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="container-fluid p-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 text-primary fw-bold">个人设置</h1>
                </div>

                <!-- 个人信息 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">个人信息</h6>
                            </div>
                            <div class="card-body">
                                <form id="profileForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="username" class="form-label fw-bold">用户名</label>
                                            <input type="text" class="form-control" id="username" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label fw-bold">邮箱地址</label>
                                            <input type="email" class="form-control" id="email" readonly>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="contribution" class="form-label fw-bold">贡献度</label>
                                            <input type="text" class="form-control" id="contribution" readonly>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="inviteCode" class="form-label fw-bold">邀请码</label>
                                            <div class="invite-code-container">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="inviteCode" readonly>
                                                    <button class="btn btn-outline-primary" type="button" onclick="copyInviteCode()">
                                                        <i class="bi bi-clipboard"></i> 复制
                                                    </button>
                                                </div>
                                                <small class="text-muted mt-2 d-block">分享此邀请码给朋友注册可获得贡献度</small>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 修改密码 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">修改密码</h6>
                            </div>
                            <div class="card-body">
                                <form id="passwordForm">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="currentPassword" class="form-label fw-bold">当前密码</label>
                                            <input type="password" class="form-control" id="currentPassword" placeholder="输入当前密码" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="newPassword" class="form-label fw-bold">新密码</label>
                                            <input type="password" class="form-control" id="newPassword" placeholder="输入新密码" required>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="confirmPassword" class="form-label fw-bold">确认新密码</label>
                                            <input type="password" class="form-control" id="confirmPassword" placeholder="再次输入新密码" required>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-change-password">
                                        <i class="bi bi-key me-2"></i>
                                        修改密码
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户统计 -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">账户统计</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <div class="stats-card">
                                            <i class="bi bi-collection text-primary"></i>
                                            <h5 id="totalMailboxes">-</h5>
                                            <p class="text-muted mb-0">邮箱总数</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stats-card">
                                            <i class="bi bi-envelope text-success"></i>
                                            <h5 id="totalEmails">0</h5>
                                            <p class="text-muted mb-0">邮件总数</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stats-card">
                                            <i class="bi bi-send text-info"></i>
                                            <h5 id="sentEmails">0</h5>
                                            <p class="text-muted mb-0">已发送</p>
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <div class="stats-card">
                                            <i class="bi bi-calendar text-warning"></i>
                                            <h5 id="joinDate">-</h5>
                                            <p class="text-muted mb-0">注册时间</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="/static/js/common.js"></script>
<script>
// 页面加载时获取用户信息
document.addEventListener('DOMContentLoaded', function() {
    loadProfile();
    loadStats();
    loadNavUsername();
});

// 加载导航栏用户名
async function loadNavUsername() {
    try {
        const response = await axios.get('/api/profile');
        if (response.data.success) {
            const user = response.data.data;
            const navUsername = document.getElementById('navUsername');
            if (navUsername && user.username) {
                navUsername.textContent = user.username;
            }
        }
    } catch (error) {
        console.error('Failed to load nav username:', error);
    }
}

async function loadProfile() {
    try {
        const response = await axios.get('/api/profile');
        if (response.data.success) {
            const user = response.data.data;
            document.getElementById('username').value = user.username;
            document.getElementById('email').value = user.email;
            document.getElementById('contribution').value = user.contribution || 0;
            document.getElementById('inviteCode').value = user.invite_code || '';
            
            // 格式化注册时间
            if (user.created_at) {
                const joinDate = new Date(user.created_at).toLocaleDateString('zh-CN');
                document.getElementById('joinDate').textContent = joinDate;
            }
        }
    } catch (error) {
        console.error('Failed to load profile:', error);
        showAlert('加载用户信息失败');
    }
}

async function loadStats() {
    try {
        // 获取用户资料
        const profileResponse = await axios.get('/api/profile');
        if (profileResponse.data.success) {
            const user = profileResponse.data.data;
            document.getElementById('joinDate').textContent = new Date(user.created_at).toLocaleDateString('zh-CN');
        }

        // 获取用户统计信息
        const statsResponse = await axios.get('/api/mailboxes/stats');
        if (statsResponse.data.success) {
            const stats = statsResponse.data.data;
            document.getElementById('totalMailboxes').textContent = stats.total_mailboxes || 0;
            document.getElementById('totalEmails').textContent = stats.total_emails || 0;
            document.getElementById('sentEmails').textContent = stats.sent_emails || 0;
        }
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// 修改密码表单提交
document.getElementById('passwordForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        showAlert('新密码和确认密码不匹配');
        return;
    }
    
    if (newPassword.length < 6) {
        showAlert('新密码长度至少6位');
        return;
    }
    
    try {
        const response = await axios.put('/api/profile/password', {
            old_password: currentPassword,
            new_password: newPassword
        });
        
        if (response.data.success) {
            showAlert('密码修改成功');
            document.getElementById('passwordForm').reset();
        } else {
            showAlert(response.data.message || '密码修改失败');
        }
    } catch (error) {
        console.error('Password change error:', error);
        if (error.response && error.response.data) {
            showAlert(error.response.data.message || '密码修改失败');
        } else {
            showAlert('网络错误，请稍后重试');
        }
    }
});

function copyInviteCode() {
    const inviteCode = document.getElementById('inviteCode').value;
    if (inviteCode) {
        navigator.clipboard.writeText(inviteCode).then(() => {
            showAlert('邀请码已复制到剪贴板');
        }).catch(() => {
            showAlert('复制失败，请手动复制');
        });
    }
}

function showAlert(message) {
    document.getElementById('alertMessage').textContent = message;
    const toast = new bootstrap.Toast(document.getElementById('alertToast'));
    toast.show();
}

async function logout() {
    try {
        await axios.post('/api/logout');
        window.location.href = '/login';
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/login';
    }
}
</script>

<!-- 全局提示框 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="alertToast" class="toast" role="alert">
        <div class="toast-header">
            <i class="bi bi-info-circle me-2"></i>
            <strong class="me-auto">系统提示</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body" id="alertMessage">
        </div>
    </div>
</div>
</body>
</html>