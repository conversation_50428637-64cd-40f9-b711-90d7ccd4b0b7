<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .reset-container {
            position: relative;
            z-index: 1;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .input-group-text {
            border: 2px solid #e9ecef;
            border-radius: 12px 0 0 12px;
            background: #f8f9fa;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .btn-outline-secondary {
            border: 2px solid #6c757d;
            border-radius: 12px;
            color: #6c757d;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            transform: translateY(-2px);
        }
        .password-strength {
            margin-top: 0.5rem;
        }
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        .floating-icons {
            position: absolute;
            animation: float 6s ease-in-out infinite;
            opacity: 0.1;
            color: white;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }
    </style>
</head>
<body>
    <!-- 浮动图标 -->
    <div class="floating-icons" style="top: 10%; left: 10%; font-size: 3rem; animation-delay: 0s;">
        <i class="bi bi-shield-check"></i>
    </div>
    <div class="floating-icons" style="top: 20%; right: 15%; font-size: 2.5rem; animation-delay: -2s;">
        <i class="bi bi-key-fill"></i>
    </div>
    <div class="floating-icons" style="bottom: 30%; left: 20%; font-size: 2rem; animation-delay: -4s;">
        <i class="bi bi-lock-fill"></i>
    </div>
    <div class="floating-icons" style="bottom: 20%; right: 10%; font-size: 2.8rem; animation-delay: -1s;">
        <i class="bi bi-arrow-repeat"></i>
    </div>

    <div class="container-fluid reset-container min-vh-100 d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                {{if .site_logo}}
                                    <img src="{{.site_logo}}" alt="{{.site_name}}" style="max-width: 120px; max-height: 60px; margin-bottom: 16px;">
                                {{else}}
                                    <i class="bi bi-shield-check text-primary" style="font-size: 3rem;"></i>
                                {{end}}
                                <h3 class="mt-3">重置密码</h3>
                                <p class="text-muted">请设置您的新密码</p>
                            </div>

                            <form id="resetPasswordForm">
                                <input type="hidden" id="token" name="token" value="">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">新密码</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password" required 
                                               placeholder="请输入新密码" minlength="6">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength">
                                        <div class="strength-bar">
                                            <div class="strength-fill" id="strengthFill"></div>
                                        </div>
                                        <small class="text-muted" id="strengthText">密码强度：弱</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">确认密码</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock-fill"></i>
                                        </span>
                                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required 
                                               placeholder="请再次输入新密码">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text" id="passwordMatch"></div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-check-circle me-2"></i>
                                        重置密码
                                    </button>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <a href="/login" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    返回登录
                                </a>
                            </div>

                            <div class="text-center mt-3">
                                <a href="/" class="text-muted small">
                                    <i class="bi bi-house me-1"></i>
                                    返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 mb-0">正在重置密码...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
    // 从URL获取token
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    if (token) {
        document.getElementById('token').value = token;
    } else {
        showAlert('无效的重置链接', 'danger');
    }

    // 密码显示/隐藏切换
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });

    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('confirmPassword');
        const icon = this.querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });

    // 密码强度检测
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        updatePasswordStrength(strength);
    });

    // 密码确认检测
    document.getElementById('confirmPassword').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        const matchDiv = document.getElementById('passwordMatch');
        
        if (confirmPassword === '') {
            matchDiv.innerHTML = '';
            return;
        }
        
        if (password === confirmPassword) {
            matchDiv.innerHTML = '<i class="bi bi-check-circle text-success me-1"></i><span class="text-success">密码匹配</span>';
        } else {
            matchDiv.innerHTML = '<i class="bi bi-x-circle text-danger me-1"></i><span class="text-danger">密码不匹配</span>';
        }
    });

    function calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 6) score += 1;
        if (password.length >= 8) score += 1;
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/[0-9]/.test(password)) score += 1;
        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        
        return Math.min(score, 4);
    }

    function updatePasswordStrength(strength) {
        const strengthFill = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');
        
        const colors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745'];
        const texts = ['弱', '一般', '较强', '强'];
        const widths = ['25%', '50%', '75%', '100%'];
        
        if (strength > 0) {
            strengthFill.style.backgroundColor = colors[strength - 1];
            strengthFill.style.width = widths[strength - 1];
            strengthText.textContent = `密码强度：${texts[strength - 1]}`;
        } else {
            strengthFill.style.width = '0%';
            strengthText.textContent = '密码强度：弱';
        }
    }

    document.getElementById('resetPasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const token = document.getElementById('token').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (!token) {
            showAlert('无效的重置链接', 'danger');
            return;
        }
        
        if (!password || password.length < 6) {
            showAlert('密码长度至少6位', 'warning');
            return;
        }
        
        if (password !== confirmPassword) {
            showAlert('两次输入的密码不一致', 'warning');
            return;
        }
        
        // 显示加载提示
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        try {
            const response = await axios.post('/api/reset-password', {
                token: token,
                password: password
            });
            
            if (response.data.success) {
                showAlert('密码重置成功！正在跳转到登录页面...', 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else {
                showAlert(response.data.message || '重置失败', 'danger');
            }
        } catch (error) {
            console.error('Reset password error:', error);
            if (error.response && error.response.data) {
                showAlert(error.response.data.message || '重置失败', 'danger');
            } else {
                showAlert('网络错误，请稍后重试', 'danger');
            }
        } finally {
            loadingModal.hide();
        }
    });

    function showAlert(message, type) {
        // 移除现有的alert
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // 创建新的alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到表单前面
        const form = document.getElementById('resetPasswordForm');
        form.parentNode.insertBefore(alertDiv, form);
        
        // 自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 8000);
    }
    </script>
</body>
</html>