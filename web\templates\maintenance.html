<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
            backdrop-filter: blur(10px);
        }

        .maintenance-icon {
            font-size: 4rem;
            color: #ffc107;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        .maintenance-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .maintenance-message {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .logo {
            max-height: 60px;
            margin-bottom: 1rem;
        }

        .admin-link {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }

        .admin-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .admin-link a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .footer {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9rem;
        }
    </style>
</head>

<body>
    <div class="maintenance-container">
        {{if .site_logo}}
        <img src="{{.site_logo}}" alt="{{.site_name}}" class="logo">
        {{end}}
        
        <div class="maintenance-icon">
            <i class="bi bi-tools"></i>
        </div>
        
        <h1 class="maintenance-title">系统维护中</h1>
        
        <div class="maintenance-message">
            {{.message}}
        </div>
        
        <div class="d-flex justify-content-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">维护中...</span>
            </div>
        </div>
        
        <div class="admin-link">
            <small>
                <i class="bi bi-person-gear me-1"></i>
                <a href="/admin/login">管理员登录</a>
            </small>
        </div>
        
        <div class="footer">
            {{.copyright}}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 每30秒刷新一次页面，检查维护状态
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>

</html>
