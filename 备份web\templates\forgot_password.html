<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - {{.site_name}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .forgot-container {
            position: relative;
            z-index: 1;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .input-group-text {
            border: 2px solid #e9ecef;
            border-radius: 12px 0 0 12px;
            background: #f8f9fa;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .btn-outline-secondary {
            border: 2px solid #6c757d;
            border-radius: 12px;
            color: #6c757d;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            transform: translateY(-2px);
        }
        .floating-icons {
            position: absolute;
            animation: float 6s ease-in-out infinite;
            opacity: 0.1;
            color: white;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }
    </style>
</head>
<body>
    <!-- 浮动图标 -->
    <div class="floating-icons" style="top: 10%; left: 10%; font-size: 3rem; animation-delay: 0s;">
        <i class="bi bi-key-fill"></i>
    </div>
    <div class="floating-icons" style="top: 20%; right: 15%; font-size: 2.5rem; animation-delay: -2s;">
        <i class="bi bi-shield-lock"></i>
    </div>
    <div class="floating-icons" style="bottom: 30%; left: 20%; font-size: 2rem; animation-delay: -4s;">
        <i class="bi bi-envelope-heart"></i>
    </div>
    <div class="floating-icons" style="bottom: 20%; right: 10%; font-size: 2.8rem; animation-delay: -1s;">
        <i class="bi bi-arrow-clockwise"></i>
    </div>

    <div class="container-fluid forgot-container min-vh-100 d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card shadow">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                {{if .site_logo}}
                                    <img src="{{.site_logo}}" alt="{{.site_name}}" style="max-width: 120px; max-height: 60px; margin-bottom: 16px;">
                                {{else}}
                                    <i class="bi bi-key-fill text-primary" style="font-size: 3rem;"></i>
                                {{end}}
                                <h3 class="mt-3">找回密码</h3>
                                <p class="text-muted">输入您的注册邮箱，我们将发送重置密码链接</p>
                            </div>

                            <form id="forgotPasswordForm">
                                <div class="mb-3">
                                    <label for="email" class="form-label">注册邮箱</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email" required 
                                               placeholder="请输入您的注册邮箱">
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        请输入您创建账户时使用的邮箱地址
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-send me-2"></i>
                                        发送重置链接
                                    </button>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <p class="mb-2">想起密码了？</p>
                                <a href="/login" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    返回登录
                                </a>
                            </div>

                            <div class="text-center mt-3">
                                <a href="/" class="text-muted small">
                                    <i class="bi bi-house me-1"></i>
                                    返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 mb-0">正在发送邮件...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
    document.getElementById('forgotPasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        
        if (!email) {
            showAlert('请输入邮箱地址', 'warning');
            return;
        }
        
        // 显示加载提示
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        loadingModal.show();
        
        try {
            const response = await axios.post('/api/forgot-password', {
                email: email
            });
            
            if (response.data.success) {
                showAlert('重置密码邮件已发送，请检查您的邮箱', 'success');
                // 清空表单
                document.getElementById('email').value = '';
            } else {
                showAlert(response.data.message || '发送失败', 'danger');
            }
        } catch (error) {
            console.error('Forgot password error:', error);
            if (error.response && error.response.data) {
                showAlert(error.response.data.message || '发送失败', 'danger');
            } else {
                showAlert('网络错误，请稍后重试', 'danger');
            }
        } finally {
            loadingModal.hide();
        }
    });

    function showAlert(message, type) {
        // 移除现有的alert
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // 创建新的alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到表单前面
        const form = document.getElementById('forgotPasswordForm');
        form.parentNode.insertBefore(alertDiv, form);
        
        // 自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 8000);
    }
    </script>
</body>
</html>